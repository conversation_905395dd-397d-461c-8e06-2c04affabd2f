[gd_scene load_steps=4 format=3 uid="uid://djb632ayh6gg0"]

[ext_resource type="Script" path="res://Scripts/ObjectivesPanel.gd" id="1_objectives"]

[sub_resource type="LabelSettings" id="LabelSettings_title"]
font_size = 20
outline_size = 3
outline_color = Color(0, 0, 0, 1)

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0, 0, 0, 0.5)
corner_radius_top_left = 8
corner_radius_top_right = 8
corner_radius_bottom_right = 8
corner_radius_bottom_left = 8

[node name="ObjectivesPanel" type="Control"]
layout_mode = 3
anchors_preset = 0
offset_right = 250.0
offset_bottom = 300.0
script = ExtResource("1_objectives")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "OBJECTIVES"
label_settings = SubResource("LabelSettings_title")
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2
