[gd_scene load_steps=2 format=3 uid="uid://c483p85jd82im"]

[ext_resource type="Script" path="res://Scripts/WinScreen.gd" id="1_win_screen"]

[node name="WinScreen" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_win_screen")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.05, 0.2, 0.05, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -120.0
offset_right = 150.0
offset_bottom = 120.0

[node name="WinTitle" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "MISSION COMPLETE!"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer1" type="Control" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="WinMessage" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "You successfully reached home!"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer2" type="Control" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 40)

[node name="RestartButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(200, 50)
text = "PLAY AGAIN"

[node name="MenuButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(200, 50)
text = "MAIN MENU"

[connection signal="pressed" from="VBoxContainer/RestartButton" to="." method="_on_restart_button_pressed"]
[connection signal="pressed" from="VBoxContainer/MenuButton" to="." method="_on_menu_button_pressed"]
