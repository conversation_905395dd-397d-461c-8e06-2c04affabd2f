[gd_scene load_steps=4 format=3 uid="uid://c8x7y2qm8qrst"]

[ext_resource type="Script" uid="uid://dbms3atxo2hfg" path="res://Scripts/Collectable.gd" id="1_collectable"]
[ext_resource type="Texture2D" uid="uid://ba4pw6xs2250w" path="res://Assets/kenney_simple-space/PNG/Default/star_medium.png" id="2_star"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 97.0052

[node name="StarCollectable" type="Area2D"]
script = ExtResource("1_collectable")
orbit_radius = 120.0
orbit_speed = 1.5

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(1, 1, 0.8, 1)
texture = ExtResource("2_star")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")

[node name="CollectionParticles" type="CPUParticles2D" parent="."]
emitting = false
amount = 15
lifetime = 0.8
explosiveness = 1.0
direction = Vector2(0, -1)
gravity = Vector2(0, 50)
initial_velocity_min = 40.0
initial_velocity_max = 80.0
scale_amount_min = 0.3
color = Color(1, 1, 0, 1)
