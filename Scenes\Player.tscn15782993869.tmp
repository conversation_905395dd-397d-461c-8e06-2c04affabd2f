[gd_scene load_steps=11 format=3 uid="uid://big3rsi88wl76"]

[ext_resource type="Script" uid="uid://dton838gyavdl" path="res://Scripts/player.gd" id="1_kyqiw"]
[ext_resource type="Texture2D" uid="uid://b3yadhtuplhg2" path="res://Assets/kenney_space-shooter-extension/PNG/Sprites X2/Ships/spaceShips_001.png" id="2_kyqiw"]
[ext_resource type="Script" uid="uid://dwottdnvtkjgy" path="res://Scenes/particle_effect.gd" id="3_gntrk"]
[ext_resource type="Script" uid="uid://vv5wxkdlfjyc" path="res://Scripts/camera_shake.gd" id="4_dewec"]
[ext_resource type="Script" uid="uid://dgc8vmme70xjb" path="res://addons/trail_2d/trail_2d.gd" id="5_5ixxa"]

[sub_resource type="CircleShape2D" id="CircleShape2D_kyqiw"]
radius = 26.0192

[sub_resource type="Curve" id="Curve_kyqiw"]
_data = [Vector2(0.503356, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="Gradient" id="Gradient_gntrk"]
colors = PackedColorArray(1, 0, 0, 1, 1, 1, 0, 1)

[sub_resource type="Gradient" id="Gradient_kyqiw"]
colors = PackedColorArray(0, 0.415686, 1, 1, 1, 1, 1, 1)

[sub_resource type="Curve" id="Curve_gntrk"]
_data = [Vector2(0, 1), 0.0, -1.65429, 0, 0, Vector2(0.312081, 0.441337), -1.15612, -1.15612, 0, 0, Vector2(1, 0), -0.540128, 0.0, 0, 0]
point_count = 3

[node name="Player" type="RigidBody2D"]
input_pickable = true
gravity_scale = 0.0
script = ExtResource("1_kyqiw")

[node name="Sprite2D" type="Sprite2D" parent="."]
rotation = 4.71239
scale = Vector2(0.453657, 0.453657)
texture = ExtResource("2_kyqiw")
vframes = 2

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_kyqiw")

[node name="Trail2D" type="Line2D" parent="CollisionShape2D"]
position = Vector2(-39, -36)
script = ExtResource("5_5ixxa")
metadata/_custom_type_script = "uid://dgc8vmme70xjb"

[node name="Line2D" type="Line2D" parent="."]

[node name="LaunchParticles" type="CPUParticles2D" parent="."]
position = Vector2(-45, 0)
emitting = false
amount = 25
lifetime = 0.4
one_shot = true
preprocess = 0.1
local_coords = true
emission_shape = 3
emission_rect_extents = Vector2(1, 2.955)
direction = Vector2(0, 0)
gravity = Vector2(-980, 0)
angle_min = -360.0
angle_max = 360.0
scale_amount_min = 0.0
scale_amount_max = 10.0
scale_amount_curve = SubResource("Curve_kyqiw")
color_ramp = SubResource("Gradient_gntrk")
hue_variation_min = -1.0
hue_variation_max = 1.0
script = ExtResource("3_gntrk")

[node name="BoostParticles" type="CPUParticles2D" parent="."]
position = Vector2(-30, 0)
emitting = false
amount = 25
lifetime = 0.5
one_shot = true
preprocess = 0.1
local_coords = true
emission_shape = 3
emission_rect_extents = Vector2(1, 2.955)
direction = Vector2(0, 0)
gravity = Vector2(-980, 0)
angle_min = -360.0
angle_max = 360.0
scale_amount_min = 2.0
scale_amount_max = 15.0
scale_amount_curve = SubResource("Curve_kyqiw")
color_ramp = SubResource("Gradient_kyqiw")
hue_variation_min = -1.0
hue_variation_max = 1.0
script = ExtResource("3_gntrk")

[node name="BoostParticles-Explosion" type="CPUParticles2D" parent="BoostParticles"]
position = Vector2(-3, 0)
emitting = false
amount = 50
lifetime = 0.6
one_shot = true
preprocess = 0.1
explosiveness = 1.0
local_coords = true
emission_shape = 1
emission_sphere_radius = 10.0
direction = Vector2(0, 0)
spread = 180.0
gravity = Vector2(0, 0)
initial_velocity_max = 100.0
radial_accel_max = 100.0
angle_min = -360.0
angle_max = 360.0
scale_amount_min = 5.0
scale_amount_max = 16.0
scale_amount_curve = SubResource("Curve_gntrk")
color_ramp = SubResource("Gradient_gntrk")
hue_variation_min = -1.0
hue_variation_max = 1.0
script = ExtResource("3_gntrk")

[node name="Camera2D" type="Camera2D" parent="."]
zoom = Vector2(0.345, 0.345)
script = ExtResource("4_dewec")
