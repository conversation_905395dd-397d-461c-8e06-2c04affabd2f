[gd_scene load_steps=3 format=3 uid="uid://ce2kgb3fjtjut"]

[ext_resource type="Script" uid="uid://b8dpyepks24nr" path="res://Scripts/Settings.gd" id="1_settings"]
[ext_resource type="Texture2D" uid="uid://b3yadhtuplhg2" path="res://Assets/kenney_space-shooter-extension/PNG/Sprites X2/Ships/spaceShips_001.png" id="2_ship_texture"]

[node name="Settings" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_settings")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.1, 0.1, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -200.0
offset_right = 200.0
offset_bottom = 200.0
grow_horizontal = 2
grow_vertical = 2

[node name="SettingsTitle" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "SETTINGS"
horizontal_alignment = 1
vertical_alignment = 1

[node name="Spacer1" type="Control" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2

[node name="ShipColorLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "Ship Color:"
horizontal_alignment = 1

[node name="Spacer2" type="Control" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ExampleShipContainer" type="CenterContainer" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 80)
layout_mode = 2

[node name="ExampleShip" type="Sprite2D" parent="VBoxContainer/ExampleShipContainer"]
rotation = 4.71239
scale = Vector2(0.6, 0.6)
texture = ExtResource("2_ship_texture")
vframes = 2

[node name="Spacer3" type="Control" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ColorSliderContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="ColorSliderLabel" type="Label" parent="VBoxContainer/ColorSliderContainer"]
layout_mode = 2
text = "Drag to change color:"
horizontal_alignment = 1

[node name="ColorSlider" type="HSlider" parent="VBoxContainer/ColorSliderContainer"]
custom_minimum_size = Vector2(300, 30)
layout_mode = 2
max_value = 1.0
step = 0.01

[node name="Spacer4" type="Control" parent="VBoxContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2

[node name="BackButton" type="Button" parent="VBoxContainer"]
custom_minimum_size = Vector2(200, 50)
layout_mode = 2
text = "BACK TO MENU"

[connection signal="value_changed" from="VBoxContainer/ColorSliderContainer/ColorSlider" to="." method="_on_color_slider_value_changed"]
[connection signal="pressed" from="VBoxContainer/BackButton" to="." method="_on_back_button_pressed"]
